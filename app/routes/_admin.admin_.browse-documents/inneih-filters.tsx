import type { GetDocumentsQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import UpdateInneihDialog from './update-inneih-dialog'
import useDeleteDocument from './use-delete-document'
import useGetDocuments from './use-get-documents'

type InneihRecordFromQuery = NonNullable<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]['extra_record']> & { __typename: 'InneihRecord' }

export default function InneihFilters() {
  const { getInneih, handlePage, page, searchInneih } = useGetDocuments()
  const [selectedRecord, setSelectedRecord] = useState<InneihRecordFromQuery>()
  const [selectedDocument, setSelectedDocument] = useState<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]>()
  const [selectedId, setSelectedId] = useState('')
  const { deleteDocument } = useDeleteDocument()

  const { isOpen, toggle } = useBoolean()
  const { isOpen: openDelete, toggle: toggleDelete } = useBoolean()

  const handleDelete = () => {
    if (!selectedId) {
      return
    }
    deleteDocument.mutate(selectedId, {
      onSuccess: () => {
        setSelectedId('')
      },
    })
  }

  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      mipa_hming: '',
      mipa_pa_hming: '',
      hmeichhe_hming: '',
      hmeichhe_pa_hming: '',
      inneih_ni: '',
      hmun: '',
      inneihtirtu: '',
    },
    onSubmit: async ({ value }) => {
      searchInneih(value)
    },
  })

  const data = getInneih.data?.getDocuments?.data || []
  const lastPage = getInneih.data?.getDocuments?.paginator_info?.last_page ?? 1

  return (
    <>
      <Card>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="registration_no"
              children={field => <field.InputField label="Registration No" />}
            />
            <form.AppField
              name="mipa_hming"
              children={field => <field.InputField label="Mipa hming" />}
            />
            <form.AppField
              name="mipa_pa_hming"
              children={field => <field.InputField label="Mipa Pa hming" />}
            />
            <form.AppField
              name="hmeichhe_hming"
              children={field => <field.InputField label="Hmeichhe hming" />}
            />

            <form.AppField
              name="hmeichhe_pa_hming"
              children={field => <field.InputField label="Hmeichhe nu hming" />}
            />
            <form.AppField
              name="inneih_ni"
              children={field => <field.InputField label="Inneih ni" type="date" />}
            />
            <form.AppField
              name="hmun"
              children={field => <field.InputField label="Hmun" />}
            />
            <form.AppField
              name="inneihtirtu"
              children={field => <field.InputField label="Inneih tir tu" />}
            />
            <div className="col-span-1">
              <Button type="submit" isLoading={getInneih.isLoading}>
                Search
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      {data?.length > 0 && (
        <div className="flex grow rounded-md bg-muted p-4">
          <Table className="w-full min-w-[1000px] table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Reg no</TableHead>
                <TableHead className="w-32">Mipa hming</TableHead>
                <TableHead className="w-32">Mipa pa hming</TableHead>
                <TableHead className="w-32">Mipa Khua</TableHead>
                <TableHead className="w-32">Hmeichhe hming</TableHead>
                <TableHead className="w-32">Hmeichhe pa hming</TableHead>
                <TableHead className="w-32">Hmeichhe Khua</TableHead>
                <TableHead className="w-32">Inneih ni</TableHead>
                <TableHead className="w-32">Hmun</TableHead>
                <TableHead className="w-32">Inneihtirtu</TableHead>
                <TableHead className="w-20 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map(item => (
                item && item.extra_record?.__typename === 'InneihRecord' && (
                  <TableRow key={item.id}>
                    <TableCell>{item.extra_record?.inneih_registration_no || '-'}</TableCell>
                    <TableCell>{item.extra_record?.mipa_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.mipa_pa_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.mipa_khua || '-'}</TableCell>
                    <TableCell>{item.extra_record?.hmeichhe_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.hmeichhe_pa_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.hmeichhe_khua || '-'}</TableCell>
                    <TableCell>{item.extra_record?.inneih_ni ? format(new Date(item.extra_record?.inneih_ni), 'yyyy-MM-dd') : '-'}</TableCell>
                    <TableCell>{item.extra_record?.hmun || '-'}</TableCell>
                    <TableCell>{item.extra_record?.inneihtirtu || '-'}</TableCell>
                    <TableCell>
                      <div className="flex justify-end gap-x-2">
                        <AppTooltip message="Update">
                          <Button
                            onClick={() => {
                              if (item.extra_record && item.extra_record.__typename === 'InneihRecord') {
                                setSelectedRecord(item.extra_record)
                                setSelectedDocument(item)
                              }
                              toggle(true)
                            }}
                            size="icon"
                            variant="success"
                          >
                            <UpdateIcon />
                          </Button>
                        </AppTooltip>
                        <AppTooltip message="Delete document">
                          <Button
                            onClick={() => {
                              if (item.id) {
                                setSelectedId(item.id)
                                toggleDelete(true)
                              }
                            }}
                            size="icon"
                            variant="destructive"
                          >
                            <DeleteIcon />
                          </Button>
                        </AppTooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                )))}
            </TableBody>
          </Table>

        </div>
      )}
      {lastPage > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePage}
          lastPage={lastPage}
        />
      )}
      {selectedRecord && selectedDocument && (
        <UpdateInneihDialog
          isOpen={isOpen}
          toggle={toggle}
          record={selectedRecord}
          _document={selectedDocument}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteDocument.isPending}
          handleConfirm={handleDelete}
          open={openDelete}
          handleOpenChange={toggleDelete}
        />
      )}
    </>
  )
}
