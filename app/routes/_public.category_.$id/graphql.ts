import { graphql } from '~/gql'

export const INNEIH_RECORD_FRAGMENT = graphql(`
  fragment InneihRecordFragment on InneihRecord {
    id
    inneih_registration_no: registration_no
    mipa_hming
    mipa_pa_hming
    mipa_khua
    hmeichhe_hming
    hmeichhe_pa_hming
    hmeichhe_khua
    inneih_ni
    hmun
    inneihtirtu
  }
`)

export const BAPTISMA_RECORD_FRAGMENT = graphql(`
  fragment BaptismaRecordFragment on BaptismaRecord {
    id
    baptisma_registration_no: registration_no
    hming
    pa_hming
    nu_hming
    pian_ni
    pian_ni_remarks
    baptisma_chan_ni
    baptisma_chan_ni_remarks
    khua
    chantirtu
  }
`)

export const GET_DOCUMENTS_BY_CATEGORY_ID = graphql(`
  query GetDocumentsByCategoryId(
    $first: Int!
    $page: Int
    $category_id: ID!
    $baptisma_filter: BaptismaRecordFilterInput
    $inneih_filter: InneihRecordFilterInput
    $others_filter: OtherRecordInput
  ) {
    getDocumentsByCategoryId(
      first: $first
      page: $page
      category_id: $category_id
      baptisma_filter: $baptisma_filter
      inneih_filter: $inneih_filter
      others_filter: $others_filter
    ) {
      paginator_info {
        last_page
      }
      data {
        id
        title
        added_date
        category {
          id
          name
        }
        extra_record {
          __typename
          ... on InneihRecord {
            ...InneihRecordFragment
          }
          ... on BaptismaRecord {
            ...BaptismaRecordFragment
          }
        }
      }
    }
  }
`)

export const GET_CATEGORY_NAME = graphql(`
  query GetCategoryName(
    $id: ID!
  ) {
    getCategoryById(
      id: $id
    ) {
      name
      parent {
        id
        name
        parent {
          id
          name
        }
      }
    }
  }
`)
