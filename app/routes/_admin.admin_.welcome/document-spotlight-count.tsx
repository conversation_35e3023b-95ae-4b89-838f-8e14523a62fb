import LoaderIcon from '~/components/icons/loader-icon'
import useGetDocumentSpotlightCount from './use-get-document-spotlight-count'

export default function DocumentSpotlightCount() {
  const { data, isLoading, isError } = useGetDocumentSpotlightCount()

  if (isError) {
    return <div className="rounded-md border p-4">Error loading spotlight count data</div>
  }

  return (
    <div className="rounded-md border border-black p-4">
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-x-2">
          <div className="font-bold">
            Total number of Documents:
          </div>
          <div>
            {isLoading
              ? <LoaderIcon className="size-4 animate-spin" />
              : (
                  <div className={`
                    flex size-12 items-center justify-center rounded-full
                    bg-green-500 text-white
                  `}
                  >
                    {data?.getDocumentSpotlightCount.document_count}
                  </div>
                )}
          </div>
        </div>
        <div className="flex items-center gap-x-2">
          <div className="font-bold">Total number of Spotlights:</div>
          <div>
            {isLoading
              ? <LoaderIcon className="size-4 animate-spin" />
              : (
                  <div className={`
                    flex size-12 items-center justify-center rounded-full
                    bg-green-500 text-white
                  `}
                  >
                    {data?.getDocumentSpotlightCount.spotlight_count}
                  </div>
                )}
          </div>
        </div>

      </div>

    </div>
  )
}
